package top.dotgo.web.oauth.config;

import cn.hutool.core.map.MapUtil;
import com.alibaba.druid.pool.DruidDataSource;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.core.userdetails.UserDetailsByNameServiceWrapper;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.TokenGranter;
import org.springframework.security.oauth2.provider.approval.ApprovalStore;
import org.springframework.security.oauth2.provider.approval.InMemoryApprovalStore;
import org.springframework.security.oauth2.provider.approval.JdbcApprovalStore;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.security.oauth2.provider.client.InMemoryClientDetailsService;
import org.springframework.security.oauth2.provider.client.JdbcClientDetailsService;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.code.InMemoryAuthorizationCodeServices;
import org.springframework.security.oauth2.provider.code.JdbcAuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider;
import top.dotgo.comm.config.DotConfig;
import top.dotgo.comm.config.auth.AuthConfig;
import top.dotgo.comm.core.constant.Const;
import top.dotgo.web.oauth.granter.MaCodeTokenGranter;
import top.dotgo.web.oauth.granter.MpCodeTokenGranter;
import top.dotgo.web.oauth.granter.QrCodeTokenGranter;
import top.dotgo.web.oauth.granter.SmsCodeTokenGranter;
import top.dotgo.web.oauth.service.impl.SingleTokenServices;
import top.dotgo.web.oauth.service.impl.UserDetailsServiceImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * oauth2 授权配置
 *
 * <AUTHOR>
 * @date 2020/2/23 18:40 星期日
 */
@AllArgsConstructor
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

    public static final String JDBC = "jdbc";
    public static final String MEMORY = "memory";
    private final DruidDataSource authDataSource;
    private final AuthenticationManager authenticationManager;
    private final UserDetailsServiceImpl userDetailsService;
    private final PasswordEncoder passwordEncoder;

    private final RedisConnectionFactory redisConnectionFactory;

    private final DotConfig dotConfig;
    private final AuthConfig authConfig;

    /**
     * jdbc授权
     *
     * @return {@link JdbcClientDetailsService}
     */
    @Bean
    public JdbcClientDetailsService jdbcClientDetailsService() {
        JdbcClientDetailsService jdbcClientDetailsService = new JdbcClientDetailsService(authDataSource);
        jdbcClientDetailsService.setPasswordEncoder(passwordEncoder);
        return jdbcClientDetailsService;
    }

    /**
     * 内存授权
     *
     * @return {@link InMemoryClientDetailsService}
     */
    @Bean
    public InMemoryClientDetailsService inMemoryClientDetailsService() {
        InMemoryClientDetailsService service = new InMemoryClientDetailsService();
        service.setClientDetailsStore(MapUtil.builder(authConfig.getMemory() != null ? authConfig.getMemory().getClientId() : "dotgo", new BaseClientDetails() {{
            setClientId(authConfig.getMemory() != null ? authConfig.getMemory().getClientId() : "dotgo");
            setClientSecret(passwordEncoder.encode(authConfig.getMemory() != null ? authConfig.getMemory().getClientSecret() : "dotgo"));
            setResourceIds(Collections.singletonList("all"));
            setAuthorizedGrantTypes(
                    Arrays.asList(Const.Comm.grant.password.name(), Const.Comm.grant.refresh_token.name(), Const.Comm.grant.sms_code.name(), Const.Comm.grant.ma_code.name(), Const.Comm.grant.mp_code.name(),
                                  Const.Comm.grant.qr_code.name()
                    ));
            setScope(Arrays.asList("read", "write"));
        }}).map());

        return service;
    }

    /**
     * 配置 授权类型
     *
     * @param clients 客户
     * @throws Exception 异常
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        if (authConfig.getMode() == null || authConfig.getMode().equals(MEMORY)) {
            clients.withClientDetails(inMemoryClientDetailsService());
        }
        else if (authConfig.getMode().equals(JDBC)) {
            clients.withClientDetails(jdbcClientDetailsService());
        }
    }

    /**
     * <p>设置令牌存储方式</p>
     * InMemoryTokenStore 在内存中存储令牌。
     * RedisTokenStore 在Redis缓存中存储令牌。
     * JwkTokenStore 支持使用JSON Web Key (JWK)验证JSON Web令牌(JwT)的子Web签名(JWS)
     * JwtTokenStore 不是真正的存储，不持久化数据，身份和访问令牌可以相互转换。
     * JdbcTokenStore 在数据库存储，需要创建相应的表存储数据
     */
    @Bean
    public TokenStore tokenStore() {
        if (authConfig.getMode() == null || authConfig.getMode().equals(MEMORY)) {
            return new RedisTokenStore(redisConnectionFactory);
        }
        else if (authConfig.getMode().equals(JDBC)) {
            return new RedisTokenStore(redisConnectionFactory);
        }
        return new RedisTokenStore(redisConnectionFactory);
    }

    /**
     * 授权信息保存策略
     *
     * @return {@link ApprovalStore}
     */
    @Bean
    public ApprovalStore approvalStore() {
        if (authConfig.getMode() == null || authConfig.getMode().equals(MEMORY)) {
            return new InMemoryApprovalStore();
        }
        else if (authConfig.getMode().equals(JDBC)) {
            return new JdbcApprovalStore(authDataSource);
        }
        return new InMemoryApprovalStore();
    }

    /**
     * 授权码模式保存策略
     *
     * @return {@link AuthorizationCodeServices}
     */
    @Bean
    public AuthorizationCodeServices authorizationCodeServices() {
        if (authConfig.getMode() == null || authConfig.getMode().equals(MEMORY)) {
            return new InMemoryAuthorizationCodeServices();
        }
        else if (authConfig.getMode().equals(JDBC)) {
            return new JdbcAuthorizationCodeServices(authDataSource);
        }
        return new InMemoryAuthorizationCodeServices();
    }

    /**
     * 对Jwt签名时，增加一个密钥
     * JwtAccessTokenConverter：对Jwt来进行编码以及解码的类
     */
    @Bean
    public JwtAccessTokenConverter accessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
        converter.setSigningKey(dotConfig.getJwtSalt());
        return converter;
    }

    /**
     * 设置token 由Jwt产生，不使用默认的透明令牌
     */
    @Bean
    public TokenEnhancer tokenEnhancer() {
        return new JwtTokenEnhancer();
    }


    /**
     * *用来配置令牌端点(Token Endpoint)的安全约束。
     */
    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) {
        //允许客户表单认证
        security.allowFormAuthenticationForClients();
        //设置oauth_client_details中的密码编码器
        security.passwordEncoder(passwordEncoder);
        //对于CheckEndpoint控制器[框架自带的校验]的/oauth/check端点允许所有客户端发送器请求而不会被Spring-security拦截
        security.checkTokenAccess("isAuthenticated()");
        // 获取token请求不进行拦截
        security.tokenKeyAccess("permitAll()");

    }

    /**
     * OAuth2的主配置信息
     *
     * @param endpoints 端点
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {

        TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
        List<TokenEnhancer> enhancers = new ArrayList<>();
        enhancers.add(tokenEnhancer());
        enhancers.add(accessTokenConverter());
        enhancerChain.setTokenEnhancers(enhancers);

        endpoints.approvalStore(approvalStore())
                 .authenticationManager(authenticationManager)
                 .authorizationCodeServices(authorizationCodeServices())
                 .tokenGranter(tokenGranter(endpoints))
                 .tokenStore(tokenStore())
                 .tokenEnhancer(enhancerChain)
                 .userDetailsService(userDetailsService)
                 .tokenServices(tokenServices(endpoints))
                 .allowedTokenEndpointRequestMethods(HttpMethod.POST, HttpMethod.GET)
                 .pathMapping("/oauth/token", "/login/token/{type}");
    }

    /**
     * 令牌 登录方式
     *
     * @param endpoints 端点
     * @return {@link TokenGranter}
     */
    private TokenGranter tokenGranter(AuthorizationServerEndpointsConfigurer endpoints) {
        List<TokenGranter> granters = new ArrayList<>(Collections.singletonList(endpoints.getTokenGranter()));

        // 构建短信验证授权类型
        SmsCodeTokenGranter smsCodeTokenGranter = new SmsCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userDetailsService);
        granters.add(smsCodeTokenGranter);

        // 构建小程序code验证授权类型
        MaCodeTokenGranter maCodeTokenGranter = new MaCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userDetailsService);
        granters.add(maCodeTokenGranter);

        // 构建公众号code验证授权类型
        MpCodeTokenGranter mpCodeTokenGranter = new MpCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userDetailsService);
        granters.add(mpCodeTokenGranter);

        //构建二维码code 验证授权类型
        QrCodeTokenGranter qrCodeTokenGranter = new QrCodeTokenGranter(endpoints.getTokenServices(), endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), userDetailsService);
        granters.add(qrCodeTokenGranter);

        // 返回所有类型
        return new CompositeTokenGranter(granters);
    }

    /***
     * 自定义 tokenService
     * 同一用户每次获取token都保证只有最后一个token能够使用，之前的token都设为无效
     * @param endpoints endpoints
     * @return {@link SingleTokenServices}
     * <AUTHOR>
     * @date 2020/2/25 10:54
     */
    private SingleTokenServices tokenServices(AuthorizationServerEndpointsConfigurer endpoints) {
        SingleTokenServices tokenServices = new SingleTokenServices();
        tokenServices.setTokenStore(tokenStore());
        //支持刷新token
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setReuseRefreshToken(false);
        tokenServices.setAccessTokenValiditySeconds(dotConfig.getJwtExpire());
        tokenServices.setRefreshTokenValiditySeconds(dotConfig.getSessionExpire());
        tokenServices.setClientDetailsService(endpoints.getClientDetailsService());
        tokenServices.setTokenEnhancer(endpoints.getTokenEnhancer());
        addUserDetailsService(tokenServices, this.userDetailsService);
        return tokenServices;
    }

    /**
     * 向自定义tokenService 中注入 userDetailsService
     *
     * @param tokenServices      tokenServices
     * @param userDetailsService userDetailsService
     */
    private void addUserDetailsService(SingleTokenServices tokenServices, UserDetailsService userDetailsService) {
        if (userDetailsService != null) {
            PreAuthenticatedAuthenticationProvider provider = new PreAuthenticatedAuthenticationProvider();
            provider.setPreAuthenticatedUserDetailsService(new UserDetailsByNameServiceWrapper<>(userDetailsService));
            tokenServices.setAuthenticationManager(new ProviderManager(Collections.singletonList(provider)));
        }
    }
}
