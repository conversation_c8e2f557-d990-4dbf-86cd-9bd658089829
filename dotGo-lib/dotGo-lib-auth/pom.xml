<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.dotgo.lib</groupId>
        <artifactId>dotGo-lib</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <description>第三方授权登录模块</description>

    <artifactId>dotGo-lib-auth</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
        </dependency>

        <dependency>
            <groupId>top.dotgo.comm</groupId>
            <artifactId>dotGo-comm-config</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>
        <dependency>
            <groupId>top.dotgo.comm</groupId>
            <artifactId>dotGo-comm-context</artifactId>
        </dependency>
    </dependencies>

</project>