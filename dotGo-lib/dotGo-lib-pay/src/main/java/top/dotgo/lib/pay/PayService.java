package top.dotgo.lib.pay;

import top.dotgo.comm.core.constant.Const;
import top.dotgo.lib.pay.bean.RefundGoodsDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 支付服务
 *
 * <AUTHOR>
 */
public interface PayService {

    /**
     * 统一下单接口
     *
     * @param orderNo 商户订单号
     * @param amount  支付金额(单位：分)
     * @param subject 订单标题
     * @param desc    订单描述
     * @param payer   支付者的openid，仅在指定支付方式时需要
     * @param attach  附加数据，在查询API和支付通知中原样返回，可作为自定义参数使用。
     * @param payWay  支付方式
     * @return 支付预下单结果(通常包含支付二维码链接或调起支付所需参数)
     */
    Object unifiedOrder(String orderNo, int amount, String subject, String desc, String payer, String attach, Const.PayTrade.payWay payWay);


    /**
     * 查询订单接口
     *
     * @param orderNo 商户订单号
     * @return 订单查询结果(通常包含订单状态 、 支付金额等信息)
     */
    Object queryOrder(String orderNo);

    /**
     * 关闭订单接口
     *
     * @param orderNo 商户订单号
     * @return 关闭订单结果(通常包含订单状态 、 支付金额等信息)
     */
    Object closeOrder(String orderNo);

    /**
     * 退款接口
     *
     * @param transactionId 第三方支付交易号
     * @param orderNo       商户订单号
     * @param refundNo      退款单号
     * @param amount        退款金额(单位：分)
     * @param goodsName     商品名称
     * @param reason        退款原因
     * @return 退款结果(通常包含退款状态 、 退款金额等信息)
     */
    Object refund(String transactionId, String orderNo, String refundNo, int amount, String goodsName, String reason);

    /**
     * 退款接口
     *
     * @param transactionId 第三方支付交易号
     * @param orderNo       商户订单号
     * @param refundNo      退款单号
     * @param goods         退款商品信息
     * @param reason        退款原因
     * @return 退款结果(通常包含退款状态 、 退款金额等信息)
     */
    Object refund(String transactionId, String orderNo, String refundNo, List<RefundGoodsDTO> goods, String reason);

    /**
     * 退款查询接口
     *
     * @param refundNo 商户退款单号
     * @return 退款查询结果(通常包含退款状态 、 退款金额等信息)
     */
    Object refundQuery(String refundNo);

    /**
     * 验证通知
     *
     * @param request 请求
     * @return 结果
     */
    Object verifyNotify(HttpServletRequest request);
}
