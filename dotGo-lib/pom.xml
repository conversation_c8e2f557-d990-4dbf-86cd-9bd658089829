<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dotGo</artifactId>
        <groupId>top.dotgo</groupId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <groupId>top.dotgo.lib</groupId>
    <artifactId>dotGo-lib</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <modules>
        <module>dotGo-lib-oss</module>
        <module>dotGo-lib-sms</module>
        <module>dotGo-lib-pay</module>
        <module>dotGo-lib-proxy</module>
        <module>dotGo-lib-auth</module>
    </modules>
    <dependencies>

    </dependencies>

</project>
