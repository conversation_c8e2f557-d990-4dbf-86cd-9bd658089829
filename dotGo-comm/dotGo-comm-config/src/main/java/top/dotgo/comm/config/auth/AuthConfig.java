package top.dotgo.comm.config.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 身份验证配置
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "dotgo.auth")
public class AuthConfig {

    /**
     * 模式
     * jdbc、memory
     */
    private String mode;

    /**
     * 内存配置
     */
    private AuthMemory memory;


    /**
     * 第三方 授权登录配置
     */
    private List<ThirdAuthConfig> third;


    /**
     * 获取第三方授权登录配置
     *
     * @param source 来源
     * @return 第三方授权登录配置
     */
    public ThirdAuthConfig getThirdAuthConfig(ThirdAuthConfig.Source source) {
        return third.stream().filter(d -> d.getSource().equals(source)).findFirst().orElseThrow(() -> new RuntimeException("未找到第三方授权登录配置"));
    }
}
