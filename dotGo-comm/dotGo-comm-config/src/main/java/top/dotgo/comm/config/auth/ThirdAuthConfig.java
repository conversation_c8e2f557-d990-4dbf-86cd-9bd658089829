package top.dotgo.comm.config.auth;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThirdAuthConfig {

    /**
     * 来源
     */
    private Source source;
    /**
     * appid key
     */
    private String key;
    /**
     * appid secret
     */
    private String secret;
    /**
     * 回调地址
     */
    private String callback;

    /**
     * 来源
     */
    @AllArgsConstructor
    public enum Source {
        /**
         * 钉钉
         */
        DINGTALK("钉钉", "dingtalk"),
        /**
         * 百度
         */
        BAIDU("百度", "baidu"),
        /**
         * github
         */
        GITHUB("github", "github"),
        /**
         * gitee
         */
        GITEE("gitee", "gitee"),
        /**
         * 微博
         */
        WEIBO("微博", "weibo"),
        /**
         * coding
         */
        CODING("coding", "coding"),
        /**
         * oschina
         */
        OSCHINA("开源中国", "oschina"),
        /**
         * alipay
         */
        ALIPAY("支付宝", "alipay"),
        /**
         * qq
         */
        QQ("qq", "qq"),
        /**
         * wechat_open
         */
        WECHAT_OPEN("微信开放平台", "wechat_open"),
        /**
         * csdn
         */
        CSDN("csdn", "csdn"),
        /**
         * taobao
         */
        TAOBAO("淘宝", "taobao"),
        /**
         * google
         */
        GOOGLE("谷歌", "google"),
        /**
         * facebook
         */
        FACEBOOK("facebook", "facebook"),
        /**
         * linkedin
         */
        LINKEDIN("linkedin", "linkedin"),
        /**
         * microsoft
         */
        MICROSOFT("微软", "microsoft"),
        /**
         * mi
         */
        MI("小米", "mi"),
        /**
         * 今日头条
         */
        TOUTIAO("今日头条", "toutiao"),
        /**
         * Teambition
         */
        TEAMBITION("Teambition", "teambition"),
        /**
         * Pinterest
         */
        PINTEREST("Pinterest", "pinterest"),
        /**
         * 人人网
         */
        RENREN("人人网", "renren"),
        /**
         * Stack Overflow
         */
        STACK_OVERFLOW("Stack Overflow", "stack_overflow"),
        /**
         * 华为
         */
        HUAWEI("华为", "huawei"),
        /**
         * 企业微信
         */
        WECHAT_ENTERPRISE("企业微信", "wechat_enterprise"),
        /**
         * 酷家乐
         */
        KUJIALE("酷家乐", "kujiale"),
        /**
         * GitLab
         */
        GITLAB("GitLab", "gitlab"),
        /**
         * 美团
         */
        MEITUAN("美团", "meituan"),
        /**
         * 饿了么
         */
        ELEME("饿了么", "eleme"),
        /**
         * MyGitLab
         */
        MYGITLAB("MyGitLab", "mygitlab"),
        /**
         * Twitter
         */
        TWITTER("Twitter", "twitter"),
        /**
         * 微信公众号
         */
        WECHAT_MP("微信公众号", "wechat_mp"),
        /**
         * 阿里云
         */
        ALIYUN("阿里云", "aliyun"),
        /**
         * 喜马拉雅
         */
        XIMALY("喜马拉雅", "xmly"),
        /**
         * 飞书
         */
        FEISHU("飞书", "feishu"),
        /**
         * 抖音
         */
        DOUYIN("抖音", "douyin"),
        /**
         * 快手
         */
        KUAISHOU("快手", "kuaishou");
        private final String name;
        private final String code;
    }
}